/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    ARW9679.c
  * @brief   ARW9679芯片驱动实现文件
  ******************************************************************************
  * @attention
  *
  * ARW9679 SPI通信芯片驱动程序实现
  * 支持温度读取和寄存器读写操作
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Includes ------------------------------------------------------------------*/
#include "ARW9679.h"

/* Private variables ---------------------------------------------------------*/

/* Private function prototypes -----------------------------------------------*/

/* Private functions ---------------------------------------------------------*/

/**
  * @brief  ARW9679芯片初始化
  * @param  harw: ARW9679句柄指针
  * @retval ARW9679_StatusTypeDef
  */
ARW9679_StatusTypeDef ARW9679_Init(ARW9679_HandleTypeDef *harw)
{
    if (harw == NULL || harw->hspi == NULL) {
        return ARW9679_INVALID_PARAM;
    }
    
    // 设置默认超时时间
    if (harw->timeout == 0) {
        harw->timeout = ARW9679_SPI_TIMEOUT;
    }
    
    // 初始化CS引脚为高电平（非选中状态）
    ARW9679_CS_High(harw);
    
    // 延时等待芯片稳定
    HAL_Delay(10);
    
    return ARW9679_OK;
}

/**
  * @brief  ARW9679芯片复位
  * @param  harw: ARW9679句柄指针
  * @retval ARW9679_StatusTypeDef
  */
ARW9679_StatusTypeDef ARW9679_Reset(ARW9679_HandleTypeDef *harw)
{
    if (harw == NULL) {
        return ARW9679_INVALID_PARAM;
    }
    
    // 写入复位寄存器
    ARW9679_StatusTypeDef status = ARW9679_WriteRegister_Short(harw, ARW9679_REG_RESET, 0x01);
    
    if (status == ARW9679_OK) {
        // 等待复位完成
        HAL_Delay(50);
    }
    
    return status;
}

/**
  * @brief  获取设备ID
  * @param  harw: ARW9679句柄指针
  * @param  device_id: 设备ID指针
  * @retval ARW9679_StatusTypeDef
  */
ARW9679_StatusTypeDef ARW9679_GetDeviceID(ARW9679_HandleTypeDef *harw, uint16_t *device_id)
{
    if (harw == NULL || device_id == NULL) {
        return ARW9679_INVALID_PARAM;
    }
    
    return ARW9679_ReadRegister_Short(harw, ARW9679_REG_DEVICE_ID, device_id);
}

/**
  * @brief  CS引脚拉低
  * @param  harw: ARW9679句柄指针
  * @retval None
  */
void ARW9679_CS_Low(ARW9679_HandleTypeDef *harw)
{
    if (harw != NULL && harw->cs_port != NULL) {
        HAL_GPIO_WritePin(harw->cs_port, harw->cs_pin, GPIO_PIN_RESET);
    }
}

/**
  * @brief  CS引脚拉高
  * @param  harw: ARW9679句柄指针
  * @retval None
  */
void ARW9679_CS_High(ARW9679_HandleTypeDef *harw)
{
    if (harw != NULL && harw->cs_port != NULL) {
        HAL_GPIO_WritePin(harw->cs_port, harw->cs_pin, GPIO_PIN_SET);
    }
}

/**
  * @brief  SPI发送数据
  * @param  harw: ARW9679句柄指针
  * @param  tx_data: 发送数据指针
  * @param  size: 数据长度
  * @retval ARW9679_StatusTypeDef
  */
ARW9679_StatusTypeDef ARW9679_SPI_Transmit(ARW9679_HandleTypeDef *harw, uint8_t *tx_data, uint16_t size)
{
    if (harw == NULL || tx_data == NULL || size == 0) {
        return ARW9679_INVALID_PARAM;
    }
    
    ARW9679_CS_Low(harw);
    HAL_StatusTypeDef hal_status = HAL_SPI_Transmit(harw->hspi, tx_data, size, harw->timeout);
    ARW9679_CS_High(harw);
    
    return (hal_status == HAL_OK) ? ARW9679_OK : ARW9679_ERROR;
}

/**
  * @brief  SPI接收数据
  * @param  harw: ARW9679句柄指针
  * @param  rx_data: 接收数据指针
  * @param  size: 数据长度
  * @retval ARW9679_StatusTypeDef
  */
ARW9679_StatusTypeDef ARW9679_SPI_Receive(ARW9679_HandleTypeDef *harw, uint8_t *rx_data, uint16_t size)
{
    if (harw == NULL || rx_data == NULL || size == 0) {
        return ARW9679_INVALID_PARAM;
    }
    
    ARW9679_CS_Low(harw);
    HAL_StatusTypeDef hal_status = HAL_SPI_Receive(harw->hspi, rx_data, size, harw->timeout);
    ARW9679_CS_High(harw);
    
    return (hal_status == HAL_OK) ? ARW9679_OK : ARW9679_ERROR;
}

/**
  * @brief  SPI发送接收数据
  * @param  harw: ARW9679句柄指针
  * @param  tx_data: 发送数据指针
  * @param  rx_data: 接收数据指针
  * @param  size: 数据长度
  * @retval ARW9679_StatusTypeDef
  */
ARW9679_StatusTypeDef ARW9679_SPI_TransmitReceive(ARW9679_HandleTypeDef *harw, uint8_t *tx_data, uint8_t *rx_data, uint16_t size)
{
    if (harw == NULL || tx_data == NULL || rx_data == NULL || size == 0) {
        return ARW9679_INVALID_PARAM;
    }
    
    ARW9679_CS_Low(harw);
    HAL_StatusTypeDef hal_status = HAL_SPI_TransmitReceive(harw->hspi, tx_data, rx_data, size, harw->timeout);
    ARW9679_CS_High(harw);
    
    return (hal_status == HAL_OK) ? ARW9679_OK : ARW9679_ERROR;
}

/**
  * @brief  短寄存器读取
  * @param  harw: ARW9679句柄指针
  * @param  reg_addr: 寄存器地址
  * @param  data: 读取数据指针
  * @retval ARW9679_StatusTypeDef
  */
ARW9679_StatusTypeDef ARW9679_ReadRegister_Short(ARW9679_HandleTypeDef *harw, uint16_t reg_addr, uint16_t *data)
{
    if (harw == NULL || data == NULL) {
        return ARW9679_INVALID_PARAM;
    }

    uint8_t tx_buf[4] = {0};
    uint8_t rx_buf[4] = {0};

    // 构造命令帧: [CMD][ADDR_H][ADDR_L][DUMMY]
    tx_buf[0] = ARW9679_CMD_SHORT_READ;
    tx_buf[1] = (reg_addr >> 8) & 0xFF;
    tx_buf[2] = reg_addr & 0xFF;
    tx_buf[3] = 0x00; // 虚拟字节

    ARW9679_StatusTypeDef status = ARW9679_SPI_TransmitReceive(harw, tx_buf, rx_buf, 4);

    if (status == ARW9679_OK) {
        // 数据在最后两个字节
        *data = (rx_buf[2] << 8) | rx_buf[3];
    }

    return status;
}

/**
  * @brief  短寄存器写入
  * @param  harw: ARW9679句柄指针
  * @param  reg_addr: 寄存器地址
  * @param  data: 写入数据
  * @retval ARW9679_StatusTypeDef
  */
ARW9679_StatusTypeDef ARW9679_WriteRegister_Short(ARW9679_HandleTypeDef *harw, uint16_t reg_addr, uint16_t data)
{
    if (harw == NULL) {
        return ARW9679_INVALID_PARAM;
    }

    uint8_t tx_buf[5] = {0};

    // 构造命令帧: [CMD][ADDR_H][ADDR_L][DATA_H][DATA_L]
    tx_buf[0] = ARW9679_CMD_SHORT_WRITE;
    tx_buf[1] = (reg_addr >> 8) & 0xFF;
    tx_buf[2] = reg_addr & 0xFF;
    tx_buf[3] = (data >> 8) & 0xFF;
    tx_buf[4] = data & 0xFF;

    return ARW9679_SPI_Transmit(harw, tx_buf, 5);
}

/**
  * @brief  长寄存器读取
  * @param  harw: ARW9679句柄指针
  * @param  reg_addr: 寄存器地址
  * @param  data: 读取数据指针
  * @param  length: 数据长度
  * @retval ARW9679_StatusTypeDef
  */
ARW9679_StatusTypeDef ARW9679_ReadRegister_Long(ARW9679_HandleTypeDef *harw, uint16_t reg_addr, uint8_t *data, uint16_t length)
{
    if (harw == NULL || data == NULL || length == 0 || length > ARW9679_MAX_DATA_LEN) {
        return ARW9679_INVALID_PARAM;
    }

    uint8_t tx_buf[4] = {0};
    uint8_t rx_buf[4] = {0};

    // 构造命令帧: [CMD][ADDR_H][ADDR_L][LENGTH]
    tx_buf[0] = ARW9679_CMD_LONG_READ;
    tx_buf[1] = (reg_addr >> 8) & 0xFF;
    tx_buf[2] = reg_addr & 0xFF;
    tx_buf[3] = length & 0xFF;

    ARW9679_CS_Low(harw);

    // 发送命令
    HAL_StatusTypeDef hal_status = HAL_SPI_TransmitReceive(harw->hspi, tx_buf, rx_buf, 4, harw->timeout);

    if (hal_status == HAL_OK) {
        // 接收数据
        hal_status = HAL_SPI_Receive(harw->hspi, data, length, harw->timeout);
    }

    ARW9679_CS_High(harw);

    return (hal_status == HAL_OK) ? ARW9679_OK : ARW9679_ERROR;
}

/**
  * @brief  长寄存器写入
  * @param  harw: ARW9679句柄指针
  * @param  reg_addr: 寄存器地址
  * @param  data: 写入数据指针
  * @param  length: 数据长度
  * @retval ARW9679_StatusTypeDef
  */
ARW9679_StatusTypeDef ARW9679_WriteRegister_Long(ARW9679_HandleTypeDef *harw, uint16_t reg_addr, uint8_t *data, uint16_t length)
{
    if (harw == NULL || data == NULL || length == 0 || length > ARW9679_MAX_DATA_LEN) {
        return ARW9679_INVALID_PARAM;
    }

    uint8_t tx_buf[4] = {0};

    // 构造命令帧: [CMD][ADDR_H][ADDR_L][LENGTH]
    tx_buf[0] = ARW9679_CMD_LONG_WRITE;
    tx_buf[1] = (reg_addr >> 8) & 0xFF;
    tx_buf[2] = reg_addr & 0xFF;
    tx_buf[3] = length & 0xFF;

    ARW9679_CS_Low(harw);

    // 发送命令
    HAL_StatusTypeDef hal_status = HAL_SPI_Transmit(harw->hspi, tx_buf, 4, harw->timeout);

    if (hal_status == HAL_OK) {
        // 发送数据
        hal_status = HAL_SPI_Transmit(harw->hspi, data, length, harw->timeout);
    }

    ARW9679_CS_High(harw);

    return (hal_status == HAL_OK) ? ARW9679_OK : ARW9679_ERROR;
}

/**
  * @brief  读取温度原始数据
  * @param  harw: ARW9679句柄指针
  * @param  temp_raw: 温度原始数据指针
  * @retval ARW9679_StatusTypeDef
  */
ARW9679_StatusTypeDef ARW9679_ReadTemperatureRaw(ARW9679_HandleTypeDef *harw, uint16_t *temp_raw)
{
    if (harw == NULL || temp_raw == NULL) {
        return ARW9679_INVALID_PARAM;
    }

    return ARW9679_ReadRegister_Short(harw, ARW9679_REG_TEMP_DATA, temp_raw);
}

/**
  * @brief  读取温度值（摄氏度）
  * @param  harw: ARW9679句柄指针
  * @param  temperature: 温度值指针
  * @retval ARW9679_StatusTypeDef
  */
ARW9679_StatusTypeDef ARW9679_ReadTemperature(ARW9679_HandleTypeDef *harw, float *temperature)
{
    if (harw == NULL || temperature == NULL) {
        return ARW9679_INVALID_PARAM;
    }

    uint16_t temp_raw = 0;
    ARW9679_StatusTypeDef status = ARW9679_ReadTemperatureRaw(harw, &temp_raw);

    if (status == ARW9679_OK) {
        // 转换为摄氏度 (根据实际芯片规格调整转换公式)
        *temperature = (float)temp_raw * ARW9679_TEMP_SCALE + ARW9679_TEMP_OFFSET;
    }

    return status;
}

/**
  * @brief  解析串口命令
  * @param  harw: ARW9679句柄指针
  * @param  cmd_str: 命令字符串
  * @param  response: 响应字符串
  * @retval ARW9679_StatusTypeDef
  */
ARW9679_StatusTypeDef ARW9679_ParseCommand(ARW9679_HandleTypeDef *harw, char *cmd_str, char *response)
{
    if (harw == NULL || cmd_str == NULL || response == NULL) {
        return ARW9679_INVALID_PARAM;
    }

    ARW9679_StatusTypeDef status = ARW9679_ERROR;
    uint16_t addr = 0;
    uint16_t data = 0;

    // 解析short_r命令: short_r_0x0000
    if (strncmp(cmd_str, "short_r_0x", 10) == 0) {
        if (sscanf(cmd_str + 10, "%hx", &addr) == 1) {
            status = ARW9679_ReadRegister_Short(harw, addr, &data);
            if (status == ARW9679_OK) {
                sprintf(response, "short_r_0x%04X: 0x%04X\r\n", addr, data);
            } else {
                sprintf(response, "Error reading register 0x%04X\r\n", addr);
            }
        } else {
            sprintf(response, "Invalid address format\r\n");
        }
    }
    // 解析short_w命令: short_w_0x0000_0x1234
    else if (strncmp(cmd_str, "short_w_0x", 10) == 0) {
        if (sscanf(cmd_str + 10, "%hx_0x%hx", &addr, &data) == 2) {
            status = ARW9679_WriteRegister_Short(harw, addr, data);
            if (status == ARW9679_OK) {
                sprintf(response, "short_w_0x%04X: 0x%04X OK\r\n", addr, data);
            } else {
                sprintf(response, "Error writing register 0x%04X\r\n", addr);
            }
        } else {
            sprintf(response, "Invalid command format\r\n");
        }
    }
    // 解析long_r命令: long_r_0x0000_8
    else if (strncmp(cmd_str, "long_r_0x", 9) == 0) {
        uint16_t length = 0;
        if (sscanf(cmd_str + 9, "%hx_%hu", &addr, &length) == 2) {
            if (length <= ARW9679_MAX_DATA_LEN) {
                uint8_t read_data[ARW9679_MAX_DATA_LEN] = {0};
                status = ARW9679_ReadRegister_Long(harw, addr, read_data, length);
                if (status == ARW9679_OK) {
                    sprintf(response, "long_r_0x%04X: ", addr);
                    for (int i = 0; i < length; i++) {
                        sprintf(response + strlen(response), "0x%02X ", read_data[i]);
                    }
                    strcat(response, "\r\n");
                } else {
                    sprintf(response, "Error reading long register 0x%04X\r\n", addr);
                }
            } else {
                sprintf(response, "Length too large (max %d)\r\n", ARW9679_MAX_DATA_LEN);
            }
        } else {
            sprintf(response, "Invalid command format\r\n");
        }
    }
    // 解析温度读取命令
    else if (strcmp(cmd_str, "temp") == 0) {
        float temperature = 0.0f;
        status = ARW9679_ReadTemperature(harw, &temperature);
        if (status == ARW9679_OK) {
            sprintf(response, "Temperature: %.2f°C\r\n", temperature);
        } else {
            sprintf(response, "Error reading temperature\r\n");
        }
    }
    else {
        sprintf(response, "Unknown command: %s\r\n", cmd_str);
        status = ARW9679_INVALID_PARAM;
    }

    return status;
}
