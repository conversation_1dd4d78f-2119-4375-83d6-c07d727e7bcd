# STM32CubeMX配置指南 - ARW9679项目

## 概述
本指南详细说明如何在STM32CubeMX中配置STM32F411项目以支持ARW9679芯片驱动。

## 1. 基本项目设置

### 1.1 创建新项目
1. 打开STM32CubeMX
2. 选择 "New Project"
3. 选择芯片型号：STM32F411CEUx (或您使用的具体型号)
4. 点击 "Start Project"

### 1.2 时钟配置
1. 进入 "Clock Configuration" 标签页
2. 设置系统时钟：
   - HSI: 16MHz (默认)
   - PLL: 启用
   - SYSCLK: 84MHz (推荐)
   - APB1: 42MHz
   - APB2: 84MHz

## 2. GPIO配置

### 2.1 SPI1引脚配置
在 "Pinout & Configuration" 标签页中配置以下引脚：

| 引脚 | 功能 | 模式 |
|------|------|------|
| PA5  | SPI1_SCK | Alternate Function Push Pull |
| PA6  | SPI1_MISO | Alternate Function Push Pull |
| PA7  | SPI1_MOSI | Alternate Function Push Pull |

### 2.2 CS引脚配置
| 引脚 | 功能 | 模式 | 初始状态 |
|------|------|------|----------|
| PA4  | GPIO_Output | Output Push Pull | High |

**配置步骤：**
1. 点击PA4引脚
2. 选择 "GPIO_Output"
3. 在左侧配置面板中设置：
   - GPIO output level: High
   - GPIO mode: Output Push Pull
   - GPIO Pull-up/Pull-down: No pull-up and no pull-down
   - Maximum output speed: High
   - User Label: ARW9679_CS

### 2.3 USART1引脚配置
| 引脚 | 功能 | 模式 |
|------|------|------|
| PA9  | USART1_TX | Alternate Function Push Pull |
| PA10 | USART1_RX | Alternate Function Push Pull |

## 3. 外设配置

### 3.1 SPI1配置
1. 在左侧 "Connectivity" 中找到 "SPI1"
2. 设置模式为 "Full-Duplex Master"
3. 参数配置：

```
Basic Parameters:
- Frame Format: Motorola
- Data Size: 8 Bits
- First Bit: MSB First

Clock Parameters:
- Prescaler: 16 (根据需要调整)
- Clock Polarity (CPOL): Low
- Clock Phase (CPHA): 1 Edge
- CRC Calculation: Disabled
- NSS Signal Type: Software
```

### 3.2 USART1配置
1. 在左侧 "Connectivity" 中找到 "USART1"
2. 设置模式为 "Asynchronous"
3. 参数配置：

```
Basic Parameters:
- Baud Rate: 115200 Bits/s
- Word Length: 8 Bits (including Parity)
- Parity: None
- Stop Bits: 1
- Data Direction: Receive and Transmit
- Over Sampling: 16 Samples

Advanced Features:
- Auto Baud Rate Detection: Disabled
- Binary Data Inversion: Disabled
- MSB First: Disabled
- Receiver Timeout: Disabled
```

### 3.3 NVIC配置
1. 进入 "NVIC" 标签页
2. 启用以下中断：
   - USART1 global interrupt: ✓ Enabled

## 4. 代码生成设置

### 4.1 项目设置
1. 进入 "Project Manager" 标签页
2. 设置项目信息：
   - Project Name: ARW9679_SPI_Driver
   - Project Location: 选择合适的路径
   - Toolchain/IDE: 选择您使用的IDE (如Keil MDK-ARM, STM32CubeIDE等)

### 4.2 代码生成选项
在 "Code Generator" 标签页中：
- ✓ Generate peripheral initialization as a pair of '.c/.h' files per peripheral
- ✓ Keep User Code when re-generating
- ✓ Delete previously generated files when not re-generated

### 4.3 高级设置
在 "Advanced Settings" 标签页中：
- 确保所有外设的 "Generated Function Calls" 都设置为 "HAL"

## 5. 生成代码

1. 点击 "GENERATE CODE" 按钮
2. 等待代码生成完成
3. 选择 "Open Project" 打开生成的项目

## 6. 后续步骤

代码生成完成后：

1. 将 `ARW9679.h` 复制到 `Core/Inc/` 目录
2. 将 `ARW9679.c` 复制到 `Core/Src/` 目录
3. 修改 `main.c` 文件，添加ARW9679相关代码
4. 编译项目

## 7. 硬件连接验证

确保硬件连接正确：

```
ARW9679    STM32F411
--------   ----------
VCC    ->  3.3V
GND    ->  GND
SCLK   ->  PA5 (SPI1_SCK)
MOSI   ->  PA7 (SPI1_MOSI)
MISO   ->  PA6 (SPI1_MISO)
CS     ->  PA4 (GPIO)
```

## 8. 调试配置

### 8.1 串口调试
- 连接USB转串口模块到PA9(TX)和PA10(RX)
- 使用串口调试工具，设置波特率115200

### 8.2 SPI调试
- 可以使用逻辑分析仪监控SPI通信
- 检查CS、SCLK、MOSI、MISO信号

## 9. 常见问题

### 9.1 SPI通信失败
- 检查时钟配置是否正确
- 验证CS引脚控制逻辑
- 确认SPI模式设置

### 9.2 串口无输出
- 检查波特率设置
- 验证TX/RX引脚连接
- 确认USART中断是否启用

### 9.3 编译错误
- 确保所有头文件路径正确
- 检查函数声明和定义是否匹配
- 验证HAL库版本兼容性

## 10. 性能优化建议

1. **SPI时钟频率**: 根据ARW9679规格调整SPI预分频器
2. **中断优先级**: 合理设置USART中断优先级
3. **缓冲区大小**: 根据实际需求调整串口接收缓冲区大小
4. **超时设置**: 根据通信距离和环境调整SPI超时时间

## 11. 扩展功能

如需添加更多功能，可以考虑：
- DMA传输以提高SPI通信效率
- 定时器用于定期温度采集
- 看门狗用于系统可靠性
- 低功耗模式支持
