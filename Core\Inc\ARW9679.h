/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    ARW9679.h
  * @brief   ARW9679芯片驱动头文件
  ******************************************************************************
  * @attention
  *
  * ARW9679 SPI通信芯片驱动程序
  * 支持温度读取和寄存器读写操作
  *
  ******************************************************************************
  */
/* USER CODE END Header */

#ifndef __ARW9679_H__
#define __ARW9679_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "spi.h"
#include <string.h>
#include <stdio.h>

/* Private defines -----------------------------------------------------------*/
// ARW9679 命令定义
#define ARW9679_CMD_SHORT_READ      0x01    // 短读命令
#define ARW9679_CMD_SHORT_WRITE     0x02    // 短写命令  
#define ARW9679_CMD_LONG_READ       0x03    // 长读命令
#define ARW9679_CMD_LONG_WRITE      0x04    // 长写命令

// ARW9679 寄存器地址定义
#define ARW9679_REG_TEMP_DATA       0x0000  // 温度数据寄存器
#define ARW9679_REG_CONFIG          0x0001  // 配置寄存器
#define ARW9679_REG_STATUS          0x0002  // 状态寄存器
#define ARW9679_REG_DEVICE_ID       0x0003  // 设备ID寄存器
#define ARW9679_REG_RESET           0x0004  // 复位寄存器

// SPI通信参数
#define ARW9679_SPI_TIMEOUT         1000    // SPI超时时间(ms)
#define ARW9679_MAX_DATA_LEN        64      // 最大数据长度

// 温度转换参数
#define ARW9679_TEMP_SCALE          0.1f    // 温度比例因子
#define ARW9679_TEMP_OFFSET         0.0f    // 温度偏移量

/* Typedef -------------------------------------------------------------------*/
typedef enum {
    ARW9679_OK = 0,
    ARW9679_ERROR,
    ARW9679_TIMEOUT,
    ARW9679_INVALID_PARAM
} ARW9679_StatusTypeDef;

typedef struct {
    SPI_HandleTypeDef *hspi;        // SPI句柄
    GPIO_TypeDef *cs_port;          // CS引脚端口
    uint16_t cs_pin;                // CS引脚
    uint32_t timeout;               // 超时时间
} ARW9679_HandleTypeDef;

/* Function prototypes -------------------------------------------------------*/
// 初始化和配置函数
ARW9679_StatusTypeDef ARW9679_Init(ARW9679_HandleTypeDef *harw);
ARW9679_StatusTypeDef ARW9679_Reset(ARW9679_HandleTypeDef *harw);
ARW9679_StatusTypeDef ARW9679_GetDeviceID(ARW9679_HandleTypeDef *harw, uint16_t *device_id);

// 寄存器读写函数
ARW9679_StatusTypeDef ARW9679_ReadRegister_Short(ARW9679_HandleTypeDef *harw, uint16_t reg_addr, uint16_t *data);
ARW9679_StatusTypeDef ARW9679_WriteRegister_Short(ARW9679_HandleTypeDef *harw, uint16_t reg_addr, uint16_t data);
ARW9679_StatusTypeDef ARW9679_ReadRegister_Long(ARW9679_HandleTypeDef *harw, uint16_t reg_addr, uint8_t *data, uint16_t length);
ARW9679_StatusTypeDef ARW9679_WriteRegister_Long(ARW9679_HandleTypeDef *harw, uint16_t reg_addr, uint8_t *data, uint16_t length);

// 温度读取函数
ARW9679_StatusTypeDef ARW9679_ReadTemperature(ARW9679_HandleTypeDef *harw, float *temperature);
ARW9679_StatusTypeDef ARW9679_ReadTemperatureRaw(ARW9679_HandleTypeDef *harw, uint16_t *temp_raw);

// 串口命令解析函数
ARW9679_StatusTypeDef ARW9679_ParseCommand(ARW9679_HandleTypeDef *harw, char *cmd_str, char *response);

// 低级SPI通信函数
ARW9679_StatusTypeDef ARW9679_SPI_Transmit(ARW9679_HandleTypeDef *harw, uint8_t *tx_data, uint16_t size);
ARW9679_StatusTypeDef ARW9679_SPI_Receive(ARW9679_HandleTypeDef *harw, uint8_t *rx_data, uint16_t size);
ARW9679_StatusTypeDef ARW9679_SPI_TransmitReceive(ARW9679_HandleTypeDef *harw, uint8_t *tx_data, uint8_t *rx_data, uint16_t size);

// CS控制函数
void ARW9679_CS_Low(ARW9679_HandleTypeDef *harw);
void ARW9679_CS_High(ARW9679_HandleTypeDef *harw);

#ifdef __cplusplus
}
#endif

#endif /* __ARW9679_H__ */
