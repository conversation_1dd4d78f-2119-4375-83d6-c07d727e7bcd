/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "spi.h"
#include "usart.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "ARW9679.h"
#include <string.h>
#include <stdio.h>
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */
// ARW9679芯片句柄
ARW9679_HandleTypeDef harw9679;

// 串口接收缓冲区
uint8_t uart_rx_buffer[256];
uint8_t uart_rx_index = 0;
uint8_t uart_cmd_ready = 0;

// 温度读取相关变量
float current_temperature = 0.0f;
uint32_t temp_read_timer = 0;
/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */
void ARW9679_Setup(void);
void UART_ProcessCommand(void);
float ReadTemperature(void);
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart);
/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{

  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_SPI1_Init();
  MX_USART1_UART_Init();
  /* USER CODE BEGIN 2 */

  // 初始化ARW9679芯片
  ARW9679_Setup();

  // 启动串口接收中断
  HAL_UART_Receive_IT(&huart1, &uart_rx_buffer[uart_rx_index], 1);

  // 发送启动信息
  char start_msg[] = "ARW9679 Driver Ready!\r\nCommands: temp, short_r_0x0000, short_w_0x0000_0x1234, long_r_0x0000_8\r\n";
  HAL_UART_Transmit(&huart1, (uint8_t*)start_msg, strlen(start_msg), 1000);

  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */

    // 处理串口命令
    if (uart_cmd_ready) {
      UART_ProcessCommand();
      uart_cmd_ready = 0;
    }

    // 每1秒读取一次温度
    if (HAL_GetTick() - temp_read_timer >= 1000) {
      current_temperature = ReadTemperature();
      temp_read_timer = HAL_GetTick();
    }

    HAL_Delay(10); // 主循环延时

  }
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

  /** Configure the main internal regulator output voltage
  */
  __HAL_RCC_PWR_CLK_ENABLE();
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE2);

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSI;
  RCC_OscInitStruct.HSIState = RCC_HSI_ON;
  RCC_OscInitStruct.HSICalibrationValue = RCC_HSICALIBRATION_DEFAULT;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSI;
  RCC_OscInitStruct.PLL.PLLM = 8;
  RCC_OscInitStruct.PLL.PLLN = 84;
  RCC_OscInitStruct.PLL.PLLP = RCC_PLLP_DIV2;
  RCC_OscInitStruct.PLL.PLLQ = 4;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV2;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_2) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */

/**
  * @brief  ARW9679芯片初始化设置
  * @param  None
  * @retval None
  */
void ARW9679_Setup(void)
{
  // 配置ARW9679句柄
  harw9679.hspi = &hspi1;           // 使用SPI1
  harw9679.cs_port = GPIOA;         // CS引脚端口 (根据实际硬件连接修改)
  harw9679.cs_pin = GPIO_PIN_4;     // CS引脚 (根据实际硬件连接修改)
  harw9679.timeout = 1000;          // 超时时间1秒

  // 初始化ARW9679
  if (ARW9679_Init(&harw9679) == ARW9679_OK) {
    char msg[] = "ARW9679 Init OK\r\n";
    HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 1000);

    // 读取设备ID
    uint16_t device_id = 0;
    if (ARW9679_GetDeviceID(&harw9679, &device_id) == ARW9679_OK) {
      char id_msg[50];
      sprintf(id_msg, "Device ID: 0x%04X\r\n", device_id);
      HAL_UART_Transmit(&huart1, (uint8_t*)id_msg, strlen(id_msg), 1000);
    }
  } else {
    char msg[] = "ARW9679 Init Failed\r\n";
    HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 1000);
  }
}

/**
  * @brief  处理串口命令
  * @param  None
  * @retval None
  */
void UART_ProcessCommand(void)
{
  char response[256];
  uart_rx_buffer[uart_rx_index] = '\0'; // 添加字符串结束符

  // 解析并执行命令
  ARW9679_ParseCommand(&harw9679, (char*)uart_rx_buffer, response);

  // 发送响应
  HAL_UART_Transmit(&huart1, (uint8_t*)response, strlen(response), 1000);

  // 重置接收缓冲区
  uart_rx_index = 0;
  memset(uart_rx_buffer, 0, sizeof(uart_rx_buffer));
}

/**
  * @brief  读取温度函数 (main函数调用)
  * @param  None
  * @retval float 温度值
  */
float ReadTemperature(void)
{
  float temperature = 0.0f;

  if (ARW9679_ReadTemperature(&harw9679, &temperature) == ARW9679_OK) {
    return temperature;
  } else {
    return -999.0f; // 错误值
  }
}

/**
  * @brief  串口接收完成回调函数
  * @param  huart: UART句柄指针
  * @retval None
  */
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
  if (huart->Instance == USART1) {
    // 检查是否收到回车或换行符
    if (uart_rx_buffer[uart_rx_index] == '\r' || uart_rx_buffer[uart_rx_index] == '\n') {
      if (uart_rx_index > 0) {
        uart_cmd_ready = 1; // 标记命令准备就绪
      }
    } else {
      uart_rx_index++;
      if (uart_rx_index >= sizeof(uart_rx_buffer) - 1) {
        uart_rx_index = 0; // 防止缓冲区溢出
      }
    }

    // 继续接收下一个字符
    HAL_UART_Receive_IT(&huart1, &uart_rx_buffer[uart_rx_index], 1);
  }
}

/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}
#ifdef USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
