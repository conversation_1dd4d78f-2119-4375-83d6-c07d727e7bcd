# ARW9679芯片驱动使用说明

## 概述
本驱动程序为ARW9679芯片提供了完整的SPI通信接口，支持温度读取和寄存器读写操作。

## 文件结构
- `Core/Inc/ARW9679.h` - 驱动头文件
- `Core/Src/ARW9679.c` - 驱动实现文件
- `Core/Src/main.c` - 主程序示例

## 硬件连接
请确保ARW9679芯片与STM32F411的连接如下：

| ARW9679引脚 | STM32F411引脚 | 功能 |
|-------------|---------------|------|
| SCLK        | PA5 (SPI1_SCK)| SPI时钟 |
| MOSI        | PA7 (SPI1_MOSI)| SPI主出从入 |
| MISO        | PA6 (SPI1_MISO)| SPI主入从出 |
| CS          | PA4           | 片选信号 |
| VCC         | 3.3V          | 电源 |
| GND         | GND           | 地 |

**注意**: CS引脚可以根据实际硬件连接修改，需要在`main.c`的`ARW9679_Setup()`函数中更新相应的端口和引脚配置。

## 主要功能

### 1. 初始化函数
```c
ARW9679_StatusTypeDef ARW9679_Init(ARW9679_HandleTypeDef *harw);
```

### 2. 寄存器读写
```c
// 短寄存器读写 (2字节)
ARW9679_StatusTypeDef ARW9679_ReadRegister_Short(ARW9679_HandleTypeDef *harw, uint16_t reg_addr, uint16_t *data);
ARW9679_StatusTypeDef ARW9679_WriteRegister_Short(ARW9679_HandleTypeDef *harw, uint16_t reg_addr, uint16_t data);

// 长寄存器读写 (可变长度)
ARW9679_StatusTypeDef ARW9679_ReadRegister_Long(ARW9679_HandleTypeDef *harw, uint16_t reg_addr, uint8_t *data, uint16_t length);
ARW9679_StatusTypeDef ARW9679_WriteRegister_Long(ARW9679_HandleTypeDef *harw, uint16_t reg_addr, uint8_t *data, uint16_t length);
```

### 3. 温度读取
```c
// 读取温度值 (摄氏度)
ARW9679_StatusTypeDef ARW9679_ReadTemperature(ARW9679_HandleTypeDef *harw, float *temperature);

// 读取温度原始数据
ARW9679_StatusTypeDef ARW9679_ReadTemperatureRaw(ARW9679_HandleTypeDef *harw, uint16_t *temp_raw);

// main函数中的温度读取函数
float ReadTemperature(void);
```

## 串口命令接口

程序支持通过串口发送命令来控制ARW9679芯片：

### 支持的命令格式：

1. **温度读取**
   ```
   temp
   ```
   返回: `Temperature: XX.XX°C`

2. **短寄存器读取**
   ```
   short_r_0x0000
   ```
   返回: `short_r_0x0000: 0xXXXX`

3. **短寄存器写入**
   ```
   short_w_0x0000_0x1234
   ```
   返回: `short_w_0x0000: 0x1234 OK`

4. **长寄存器读取**
   ```
   long_r_0x0000_8
   ```
   返回: `long_r_0x0000: 0xXX 0xXX 0xXX 0xXX 0xXX 0xXX 0xXX 0xXX`

### 串口配置
- 波特率: 115200 (在CubeMX中配置)
- 数据位: 8
- 停止位: 1
- 校验位: 无

## 使用示例

### 1. 基本初始化
```c
#include "ARW9679.h"

ARW9679_HandleTypeDef harw9679;

void setup_arw9679(void) {
    harw9679.hspi = &hspi1;
    harw9679.cs_port = GPIOA;
    harw9679.cs_pin = GPIO_PIN_4;
    harw9679.timeout = 1000;
    
    if (ARW9679_Init(&harw9679) == ARW9679_OK) {
        // 初始化成功
    }
}
```

### 2. 读取温度
```c
float temperature;
if (ARW9679_ReadTemperature(&harw9679, &temperature) == ARW9679_OK) {
    printf("Temperature: %.2f°C\n", temperature);
}
```

### 3. 寄存器操作
```c
uint16_t data;
// 读取寄存器
if (ARW9679_ReadRegister_Short(&harw9679, 0x0000, &data) == ARW9679_OK) {
    printf("Register 0x0000: 0x%04X\n", data);
}

// 写入寄存器
ARW9679_WriteRegister_Short(&harw9679, 0x0001, 0x1234);
```

## 注意事项

1. **CS引脚配置**: 确保在CubeMX中将CS引脚配置为GPIO输出模式
2. **SPI配置**: 确保SPI1已正确配置，建议使用以下参数：
   - 模式: Master
   - 数据大小: 8位
   - 时钟极性: Low
   - 时钟相位: 1 Edge
   - 波特率: 根据芯片规格设置

3. **温度转换**: 当前温度转换公式为简单的线性转换，可能需要根据实际芯片规格调整`ARW9679_TEMP_SCALE`和`ARW9679_TEMP_OFFSET`常量

4. **错误处理**: 所有函数都返回状态码，建议在实际应用中检查返回值

## 自定义配置

如需修改芯片参数，请编辑`ARW9679.h`文件中的以下定义：

```c
#define ARW9679_CMD_SHORT_READ      0x01    // 短读命令
#define ARW9679_CMD_SHORT_WRITE     0x02    // 短写命令  
#define ARW9679_CMD_LONG_READ       0x03    // 长读命令
#define ARW9679_CMD_LONG_WRITE      0x04    // 长写命令

#define ARW9679_REG_TEMP_DATA       0x0000  // 温度数据寄存器
#define ARW9679_TEMP_SCALE          0.1f    // 温度比例因子
#define ARW9679_TEMP_OFFSET         0.0f    // 温度偏移量
```

## 编译和使用

1. 将`ARW9679.h`和`ARW9679.c`文件添加到您的STM32项目中
2. 在CubeMX中配置SPI1和USART1
3. 确保硬件连接正确
4. 编译并下载程序
5. 通过串口工具发送命令测试功能
