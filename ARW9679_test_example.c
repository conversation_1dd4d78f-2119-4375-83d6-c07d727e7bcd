/**
  ******************************************************************************
  * @file    ARW9679_test_example.c
  * @brief   ARW9679芯片测试示例代码
  ******************************************************************************
  * 这个文件展示了如何使用ARW9679驱动程序的各种功能
  * 可以将这些代码片段集成到您的main.c中进行测试
  ******************************************************************************
  */

#include "ARW9679.h"
#include "main.h"
#include <stdio.h>

// 全局变量
extern ARW9679_HandleTypeDef harw9679;
extern UART_HandleTypeDef huart1;

/**
  * @brief  ARW9679基本功能测试
  * @param  None
  * @retval None
  */
void ARW9679_BasicTest(void)
{
    char msg[100];
    
    // 1. 测试设备ID读取
    uint16_t device_id = 0;
    if (ARW9679_GetDeviceID(&harw9679, &device_id) == ARW9679_OK) {
        sprintf(msg, "Device ID: 0x%04X\r\n", device_id);
        HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 1000);
    } else {
        sprintf(msg, "Failed to read Device ID\r\n");
        HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 1000);
    }
    
    // 2. 测试温度读取
    float temperature = 0.0f;
    if (ARW9679_ReadTemperature(&harw9679, &temperature) == ARW9679_OK) {
        sprintf(msg, "Temperature: %.2f°C\r\n", temperature);
        HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 1000);
    } else {
        sprintf(msg, "Failed to read temperature\r\n");
        HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 1000);
    }
    
    // 3. 测试温度原始数据读取
    uint16_t temp_raw = 0;
    if (ARW9679_ReadTemperatureRaw(&harw9679, &temp_raw) == ARW9679_OK) {
        sprintf(msg, "Temperature Raw: 0x%04X (%d)\r\n", temp_raw, temp_raw);
        HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 1000);
    } else {
        sprintf(msg, "Failed to read raw temperature\r\n");
        HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 1000);
    }
}

/**
  * @brief  ARW9679寄存器读写测试
  * @param  None
  * @retval None
  */
void ARW9679_RegisterTest(void)
{
    char msg[100];
    uint16_t read_data = 0;
    uint16_t write_data = 0x1234;
    
    // 1. 测试短寄存器写入
    if (ARW9679_WriteRegister_Short(&harw9679, ARW9679_REG_CONFIG, write_data) == ARW9679_OK) {
        sprintf(msg, "Write Register 0x%04X: 0x%04X OK\r\n", ARW9679_REG_CONFIG, write_data);
        HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 1000);
    } else {
        sprintf(msg, "Write Register 0x%04X Failed\r\n", ARW9679_REG_CONFIG);
        HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 1000);
    }
    
    // 2. 测试短寄存器读取
    if (ARW9679_ReadRegister_Short(&harw9679, ARW9679_REG_CONFIG, &read_data) == ARW9679_OK) {
        sprintf(msg, "Read Register 0x%04X: 0x%04X\r\n", ARW9679_REG_CONFIG, read_data);
        HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 1000);
        
        // 验证读写是否一致
        if (read_data == write_data) {
            sprintf(msg, "Register Read/Write Test PASSED\r\n");
        } else {
            sprintf(msg, "Register Read/Write Test FAILED\r\n");
        }
        HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 1000);
    } else {
        sprintf(msg, "Read Register 0x%04X Failed\r\n", ARW9679_REG_CONFIG);
        HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 1000);
    }
}

/**
  * @brief  ARW9679长寄存器读写测试
  * @param  None
  * @retval None
  */
void ARW9679_LongRegisterTest(void)
{
    char msg[200];
    uint8_t write_data[8] = {0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08};
    uint8_t read_data[8] = {0};
    
    // 1. 测试长寄存器写入
    if (ARW9679_WriteRegister_Long(&harw9679, 0x0010, write_data, 8) == ARW9679_OK) {
        sprintf(msg, "Write Long Register 0x0010: ");
        for (int i = 0; i < 8; i++) {
            sprintf(msg + strlen(msg), "0x%02X ", write_data[i]);
        }
        strcat(msg, "OK\r\n");
        HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 1000);
    } else {
        sprintf(msg, "Write Long Register 0x0010 Failed\r\n");
        HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 1000);
    }
    
    // 2. 测试长寄存器读取
    if (ARW9679_ReadRegister_Long(&harw9679, 0x0010, read_data, 8) == ARW9679_OK) {
        sprintf(msg, "Read Long Register 0x0010: ");
        for (int i = 0; i < 8; i++) {
            sprintf(msg + strlen(msg), "0x%02X ", read_data[i]);
        }
        strcat(msg, "\r\n");
        HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 1000);
        
        // 验证读写是否一致
        int match = 1;
        for (int i = 0; i < 8; i++) {
            if (read_data[i] != write_data[i]) {
                match = 0;
                break;
            }
        }
        
        if (match) {
            sprintf(msg, "Long Register Read/Write Test PASSED\r\n");
        } else {
            sprintf(msg, "Long Register Read/Write Test FAILED\r\n");
        }
        HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 1000);
    } else {
        sprintf(msg, "Read Long Register 0x0010 Failed\r\n");
        HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 1000);
    }
}

/**
  * @brief  ARW9679命令解析测试
  * @param  None
  * @retval None
  */
void ARW9679_CommandTest(void)
{
    char response[256];
    
    // 测试各种命令
    char* test_commands[] = {
        "temp",
        "short_r_0x0000",
        "short_w_0x0001_0x5678",
        "short_r_0x0001",
        "long_r_0x0000_4",
        "invalid_command"
    };
    
    int num_commands = sizeof(test_commands) / sizeof(test_commands[0]);
    
    for (int i = 0; i < num_commands; i++) {
        char msg[100];
        sprintf(msg, "Testing command: %s\r\n", test_commands[i]);
        HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 1000);
        
        // 执行命令
        ARW9679_ParseCommand(&harw9679, test_commands[i], response);
        
        // 发送响应
        HAL_UART_Transmit(&huart1, (uint8_t*)response, strlen(response), 1000);
        
        HAL_Delay(500); // 延时500ms
    }
}

/**
  * @brief  ARW9679连续温度监测测试
  * @param  duration_seconds: 监测持续时间(秒)
  * @retval None
  */
void ARW9679_ContinuousTemperatureTest(uint32_t duration_seconds)
{
    char msg[100];
    uint32_t start_time = HAL_GetTick();
    uint32_t last_read_time = 0;
    
    sprintf(msg, "Starting continuous temperature monitoring for %lu seconds...\r\n", duration_seconds);
    HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 1000);
    
    while ((HAL_GetTick() - start_time) < (duration_seconds * 1000)) {
        // 每秒读取一次温度
        if ((HAL_GetTick() - last_read_time) >= 1000) {
            float temperature = 0.0f;
            if (ARW9679_ReadTemperature(&harw9679, &temperature) == ARW9679_OK) {
                sprintf(msg, "[%lu] Temperature: %.2f°C\r\n", 
                       (HAL_GetTick() - start_time) / 1000, temperature);
                HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 1000);
            } else {
                sprintf(msg, "[%lu] Temperature read failed\r\n", 
                       (HAL_GetTick() - start_time) / 1000);
                HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 1000);
            }
            last_read_time = HAL_GetTick();
        }
        HAL_Delay(100);
    }
    
    sprintf(msg, "Temperature monitoring completed.\r\n");
    HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 1000);
}

/**
  * @brief  运行所有ARW9679测试
  * @param  None
  * @retval None
  */
void ARW9679_RunAllTests(void)
{
    char msg[] = "\r\n=== ARW9679 Driver Test Suite ===\r\n";
    HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 1000);
    
    // 1. 基本功能测试
    sprintf(msg, "\r\n1. Basic Function Test:\r\n");
    HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 1000);
    ARW9679_BasicTest();
    
    // 2. 寄存器读写测试
    sprintf(msg, "\r\n2. Register Read/Write Test:\r\n");
    HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 1000);
    ARW9679_RegisterTest();
    
    // 3. 长寄存器读写测试
    sprintf(msg, "\r\n3. Long Register Read/Write Test:\r\n");
    HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 1000);
    ARW9679_LongRegisterTest();
    
    // 4. 命令解析测试
    sprintf(msg, "\r\n4. Command Parse Test:\r\n");
    HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 1000);
    ARW9679_CommandTest();
    
    // 5. 连续温度监测测试 (10秒)
    sprintf(msg, "\r\n5. Continuous Temperature Test:\r\n");
    HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 1000);
    ARW9679_ContinuousTemperatureTest(10);
    
    sprintf(msg, "\r\n=== All Tests Completed ===\r\n");
    HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 1000);
}
